GIT
  remote: https://github.com/ruby/debug.git
  revision: 14c8a546242a5e88ed8f47607629ffbef7d3315d
  specs:
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)

PATH
  remote: ../sentry-ruby
  specs:
    sentry-ruby (5.26.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)

PATH
  remote: .
  specs:
    sentry-rails (5.26.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.26.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    ast (2.4.3)
    base64 (0.3.0)
    benchmark (0.4.1)
    benchmark-ips (2.5.0)
    benchmark-ipsa (0.2.0)
      benchmark-ips (~> 2.5.0)
      memory_profiler (~> 0.9.6)
    benchmark-memory (0.1.2)
      memory_profiler (~> 0.9)
    benchmark_driver (0.16.5)
    bigdecimal (3.2.2)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    diff-lcs (1.6.2)
    docile (1.4.1)
    drb (2.2.3)
    erb (5.0.2)
    erubi (1.13.1)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.13.2)
    language_server-protocol (********)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    memory_profiler (0.9.14)
    mini_magick (5.3.1)
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    mutex_m (0.3.0)
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.9-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-musl)
      racc (~> 1.4)
    ostruct (0.6.3)
    parallel (1.27.0)
    parser (3.3.9.0)
      ast (~> 2.4.1)
      racc
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.6)
      date
      stringio
    racc (1.8.1)
    rack (3.2.0)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (12.3.3)
    rbs (3.9.4)
      logger
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.11.2)
    reline (0.6.2)
      io-console (~> 0.5)
    rexml (3.4.1)
    rspec (3.13.1)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.5)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.2)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-retry (0.6.2)
      rspec-core (> 3.3)
    rspec-support (3.13.5)
    rubocop (1.80.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.46.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.46.0)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-packaging (0.6.0)
      lint_roller (~> 1.1.0)
      rubocop (>= 1.72.1, < 2.0)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.33.3)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-lsp (0.26.1)
      language_server-protocol (~> 3.17.0)
      prism (>= 1.2, < 2.0)
      rbs (>= 3, < 5)
    ruby-lsp-rspec (0.1.27)
      ruby-lsp (~> 0.26.0)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-cobertura (1.4.2)
      simplecov (~> 0.8)
    simplecov-html (0.13.2)
    simplecov_json_formatter (0.1.4)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sqlite3 (2.7.3-aarch64-linux-gnu)
    sqlite3 (2.7.3-aarch64-linux-musl)
    sqlite3 (2.7.3-arm-linux-gnu)
    sqlite3 (2.7.3-arm-linux-musl)
    sqlite3 (2.7.3-arm64-darwin)
    sqlite3 (2.7.3-x86_64-darwin)
    sqlite3 (2.7.3-x86_64-linux-gnu)
    sqlite3 (2.7.3-x86_64-linux-musl)
    stringio (3.1.7)
    thor (1.4.0)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.5)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin
  x86_64-darwin
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  activerecord-jdbcmysql-adapter
  base64
  benchmark
  benchmark-ips
  benchmark-ipsa
  benchmark-memory
  benchmark_driver
  debug!
  drb
  irb
  jdbc-sqlite3
  mini_magick
  mutex_m
  ostruct
  psych (>= 4.0.0)
  rails (~> 8.0)
  rake (~> 12.0)
  rexml
  rspec
  rspec-rails
  rspec-retry
  rubocop-packaging
  rubocop-rails-omakase
  ruby-lsp-rspec
  sentry-rails!
  sentry-ruby!
  simplecov
  simplecov-cobertura (~> 1.4)
  sprockets-rails
  sqlite3 (~> 2.0)

BUNDLED WITH
   2.7.1
